version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: game2048_postgres
    environment:
      POSTGRES_DB: game2048
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../backend/migrations:/docker-entrypoint-initdb.d
    networks:
      - game2048_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: game2048_redis
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - game2048_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: game2048_backend
    # Remove direct port exposure since nginx will handle it
    ports:
      - "127.0.0.1:6060:6060"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=game2048
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVER_PORT=6060
      - SERVER_HOST=0.0.0.0
      - GIN_MODE=release
      - STATIC_FILES_EMBEDDED=true
    env_file:
      - ../.env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - game2048_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6060/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  game2048_network:
    driver: bridge
