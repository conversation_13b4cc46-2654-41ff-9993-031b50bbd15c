# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=game2048
DB_USER=postgres
DB_PASSWORD=password
DB_SSL_MODE=disable

# Redis Configuration (optional, for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Server Configuration
SERVER_PORT=6060
SERVER_HOST=0.0.0.0
GIN_MODE=release
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# OAuth2 Configuration
# Provider type: custom, google, github, etc.
OAUTH2_PROVIDER=custom
OAUTH2_CLIENT_ID=your_oauth2_client_id
OAUTH2_CLIENT_SECRET=your_oauth2_client_secret
OAUTH2_REDIRECT_URL=http://localhost:6060/auth/callback

# Custom OAuth2 Endpoints (required for custom provider)
OAUTH2_AUTH_URL=https://connect.linux.do/oauth2/authorize
OAUTH2_TOKEN_URL=https://connect.linux.do/oauth2/token
OAUTH2_USERINFO_URL=https://connect.linux.do/api/user
OAUTH2_SCOPES=openid,profile,email

# User Info Field Mappings (customize based on your OAuth2 response)
OAUTH2_USER_ID_FIELD=id
OAUTH2_USER_EMAIL_FIELD=email
OAUTH2_USER_NAME_FIELD=username
OAUTH2_USER_AVATAR_FIELD=avatar_url

# Game Configuration
VICTORY_TILE=16384
MAX_CONCURRENT_GAMES=1000
GAME_SESSION_TIMEOUT=3600

# Leaderboard Configuration
LEADERBOARD_CACHE_TTL=300
MAX_LEADERBOARD_ENTRIES=100

# Development Configuration
DEBUG=false
LOG_LEVEL=info
CORS_ORIGINS=http://localhost:3000,http://localhost:6060

# Production Configuration
STATIC_FILES_EMBEDDED=true
ENABLE_METRICS=true
ENABLE_HEALTH_CHECK=true
