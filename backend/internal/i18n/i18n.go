package i18n

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"
)

// Localizer 国际化管理器
type Localizer struct {
	defaultLang string
	messages    map[string]map[string]string // [language][key]message
	mu          sync.RWMutex
}

// NewLocalizer 创建新的国际化管理器
func NewLocalizer(defaultLang string) *Localizer {
	return &Localizer{
		defaultLang: defaultLang,
		messages:    make(map[string]map[string]string),
	}
}

// LoadFromFS 从文件系统加载语言包
func (l *Localizer) LoadFromFS(fsys fs.FS, localesDir string) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	return fs.WalkDir(fsys, localesDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() || !strings.HasSuffix(path, ".json") {
			return nil
		}

		// 从文件名提取语言代码
		filename := filepath.Base(path)
		lang := strings.TrimSuffix(filename, ".json")

		// 读取文件内容
		data, err := fs.ReadFile(fsys, path)
		if err != nil {
			return fmt.Errorf("failed to read locale file %s: %w", path, err)
		}

		// 解析JSON
		var messages map[string]string
		if err := json.Unmarshal(data, &messages); err != nil {
			return fmt.Errorf("failed to parse locale file %s: %w", path, err)
		}

		l.messages[lang] = messages
		return nil
	})
}

// LoadFromDir 从目录加载语言包
func (l *Localizer) LoadFromDir(localesDir string) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	files, err := filepath.Glob(filepath.Join(localesDir, "*.json"))
	if err != nil {
		return err
	}

	for _, file := range files {
		// 从文件名提取语言代码
		filename := filepath.Base(file)
		lang := strings.TrimSuffix(filename, ".json")

		// 读取并解析文件
		data, err := os.ReadFile(file)
		if err != nil {
			return fmt.Errorf("failed to read locale file %s: %w", file, err)
		}

		var messages map[string]string
		if err := json.Unmarshal(data, &messages); err != nil {
			return fmt.Errorf("failed to parse locale file %s: %w", file, err)
		}

		l.messages[lang] = messages
	}

	return nil
}

// T 翻译函数
func (l *Localizer) T(lang, key string, args ...interface{}) string {
	l.mu.RLock()
	defer l.mu.RUnlock()

	// 尝试获取指定语言的翻译
	if messages, exists := l.messages[lang]; exists {
		if message, exists := messages[key]; exists {
			if len(args) > 0 {
				return fmt.Sprintf(message, args...)
			}
			return message
		}
	}

	// 回退到默认语言
	if lang != l.defaultLang {
		if messages, exists := l.messages[l.defaultLang]; exists {
			if message, exists := messages[key]; exists {
				if len(args) > 0 {
					return fmt.Sprintf(message, args...)
				}
				return message
			}
		}
	}

	// 如果都没找到，返回key本身
	return key
}

// GetAvailableLanguages 获取可用语言列表
func (l *Localizer) GetAvailableLanguages() []string {
	l.mu.RLock()
	defer l.mu.RUnlock()

	langs := make([]string, 0, len(l.messages))
	for lang := range l.messages {
		langs = append(langs, lang)
	}
	return langs
}

// HasLanguage 检查是否支持指定语言
func (l *Localizer) HasLanguage(lang string) bool {
	l.mu.RLock()
	defer l.mu.RUnlock()

	_, exists := l.messages[lang]
	return exists
}

// 全局实例
var globalLocalizer *Localizer

// Init 初始化全局国际化管理器
func Init(defaultLang string) {
	globalLocalizer = NewLocalizer(defaultLang)
}

// T 全局翻译函数
func T(lang, key string, args ...interface{}) string {
	if globalLocalizer == nil {
		return key
	}
	return globalLocalizer.T(lang, key, args...)
}

// LoadFromFS 全局加载函数
func LoadFromFS(fsys fs.FS, localesDir string) error {
	if globalLocalizer == nil {
		return fmt.Errorf("localizer not initialized")
	}
	return globalLocalizer.LoadFromFS(fsys, localesDir)
}

// LoadFromDir 全局加载函数
func LoadFromDir(localesDir string) error {
	if globalLocalizer == nil {
		return fmt.Errorf("localizer not initialized")
	}
	return globalLocalizer.LoadFromDir(localesDir)
}

// GetAvailableLanguages 获取可用语言
func GetAvailableLanguages() []string {
	if globalLocalizer == nil {
		return []string{}
	}
	return globalLocalizer.GetAvailableLanguages()
}

// HasLanguage 检查语言支持
func HasLanguage(lang string) bool {
	if globalLocalizer == nil {
		return false
	}
	return globalLocalizer.HasLanguage(lang)
}
