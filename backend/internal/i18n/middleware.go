package i18n

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	// ContextKeyLang 语言上下文键
	ContextKeyLang = "lang"
	// CookieLang 语言Cookie名称
	CookieLang = "lang"
	// HeaderAcceptLanguage Accept-Language头
	HeaderAcceptLanguage = "Accept-Language"
	// DefaultLanguage 默认语言
	DefaultLanguage = "en-US"
)

// LanguageMiddleware 语言检测中间件
func LanguageMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		lang := detectLanguage(c)
		c.Set(ContextKeyLang, lang)
		c.Next()
	}
}

// detectLanguage 检测用户语言偏好
func detectLanguage(c *gin.Context) string {
	// 1. 优先检查URL参数
	if lang := c.Query("lang"); lang != "" && HasLanguage(lang) {
		// 设置Cookie保存用户选择
		c.SetCookie(CookieLang, lang, 86400*30, "/", "", false, false) // 30天
		return lang
	}

	// 2. 检查Cookie中的语言设置
	if lang, err := c.<PERSON>(CookieLang); err == nil && HasLanguage(lang) {
		return lang
	}

	// 3. 检查Accept-Language头
	if lang := parseAcceptLanguage(c.GetHeader(HeaderAcceptLanguage)); lang != "" && HasLanguage(lang) {
		return lang
	}

	// 4. 返回默认语言
	return DefaultLanguage
}

// parseAcceptLanguage 解析Accept-Language头
func parseAcceptLanguage(acceptLang string) string {
	if acceptLang == "" {
		return ""
	}

	// 支持的语言映射
	langMap := map[string]string{
		"zh":    "zh-CN",
		"zh-cn": "zh-CN",
		"zh-tw": "zh-CN", // 暂时映射到简体中文
		"zh-hk": "zh-CN",
		"en":    "en-US",
		"en-us": "en-US",
		"en-gb": "en-US",
		"ja":    "ja-JP",
		"ja-jp": "ja-JP",
		"ko":    "ko-KR",
		"ko-kr": "ko-KR",
		"fr":    "fr-FR",
		"fr-fr": "fr-FR",
	}

	// 解析Accept-Language头
	languages := strings.Split(acceptLang, ",")
	for _, lang := range languages {
		// 移除权重信息 (如 en-US;q=0.9)
		lang = strings.TrimSpace(strings.Split(lang, ";")[0])
		lang = strings.ToLower(lang)

		// 直接匹配
		if mappedLang, exists := langMap[lang]; exists {
			return mappedLang
		}

		// 尝试匹配语言前缀 (如 zh-Hans -> zh)
		if parts := strings.Split(lang, "-"); len(parts) > 1 {
			if mappedLang, exists := langMap[parts[0]]; exists {
				return mappedLang
			}
		}
	}

	return ""
}

// GetLang 从上下文获取当前语言
func GetLang(c *gin.Context) string {
	if lang, exists := c.Get(ContextKeyLang); exists {
		if langStr, ok := lang.(string); ok {
			return langStr
		}
	}
	return DefaultLanguage
}

// SetLang 设置语言到上下文和Cookie
func SetLang(c *gin.Context, lang string) {
	if HasLanguage(lang) {
		c.Set(ContextKeyLang, lang)
		c.SetCookie(CookieLang, lang, 86400*30, "/", "", false, false)
	}
}

// TFunc 创建模板翻译函数
func TFunc(c *gin.Context) func(string, ...interface{}) string {
	lang := GetLang(c)
	return func(key string, args ...interface{}) string {
		return T(lang, key, args...)
	}
}

// LanguageHandler 语言切换API处理器
func LanguageHandler(c *gin.Context) {
	lang := c.PostForm("lang")
	if lang == "" {
		lang = c.Query("lang")
	}

	if lang == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "language parameter is required",
		})
		return
	}

	if !HasLanguage(lang) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":     "unsupported language",
			"supported": GetAvailableLanguages(),
		})
		return
	}

	SetLang(c, lang)
	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"language": lang,
		"message":  T(lang, "language_changed"),
	})
}

// GetLanguagesHandler 获取支持的语言列表
func GetLanguagesHandler(c *gin.Context) {
	languages := GetAvailableLanguages()
	langInfo := make([]map[string]string, 0, len(languages))

	for _, lang := range languages {
		langInfo = append(langInfo, map[string]string{
			"code": lang,
			"name": T(lang, "language_name"),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"languages": langInfo,
		"current":   GetLang(c),
	})
}
