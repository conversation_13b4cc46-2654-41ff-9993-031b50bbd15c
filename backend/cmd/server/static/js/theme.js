class ThemeToggle {
    constructor() {
        this.btn = document.getElementById('theme-toggle');
        if (!this.btn) return;
        this.applySaved();
        this.btn.addEventListener('click', () => this.toggle());

        // Listen for language changes to update button text
        document.addEventListener('languagechange', () => {
            this.updateButtonText();
        });
    }

    applySaved() {
        const saved = localStorage.getItem('theme');
        if (saved === 'dark') {
            document.body.classList.add('dark-mode');
        }
        this.updateButtonText();
    }

    updateButtonText() {
        if (!this.btn) return;

        const isDark = document.body.classList.contains('dark-mode');
        if (window.t) {
            this.btn.textContent = isDark ? window.t('light_mode') : window.t('dark_mode');
        } else {
            this.btn.textContent = isDark ? 'Light Mode' : 'Dark Mode';
        }
    }

    toggle() {
        if (document.body.classList.contains('dark-mode')) {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('theme', 'light');
        } else {
            document.body.classList.add('dark-mode');
            localStorage.setItem('theme', 'dark');
        }
        this.updateButtonText();

        // Notify about theme change
        if (window.t) {
            // Could show a notification here if needed
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    window.themeToggle = new ThemeToggle();
});
