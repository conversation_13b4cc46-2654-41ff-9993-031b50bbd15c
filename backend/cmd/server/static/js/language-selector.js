// Language Selector Component
class LanguageSelector {
    constructor() {
        this.isOpen = false;
        this.languages = [];
        this.currentLang = 'en-US';
        this.init();
    }
    
    async init() {
        // Get current language from game data or i18n
        if (window.gameData && window.gameData.lang) {
            this.currentLang = window.gameData.lang;
        } else if (window.i18n) {
            this.currentLang = window.i18n.getCurrentLanguage();
        }
        
        // Load available languages
        await this.loadLanguages();
        
        // Create UI
        this.createUI();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Listen for language changes
        document.addEventListener('languagechange', (e) => {
            this.currentLang = e.detail.language;
            this.updateUI();
        });
    }
    
    async loadLanguages() {
        try {
            const response = await fetch('/api/language');
            const data = await response.json();
            this.languages = data.languages || [];
            this.currentLang = data.current || this.currentLang;
        } catch (error) {
            console.error('Failed to load languages:', error);
            // Fallback languages
            this.languages = [
                { code: 'zh-CN', name: '简体中文' },
                { code: 'en-US', name: 'English' },
                { code: 'ja-JP', name: '日本語' },
                { code: 'ko-KR', name: '한국어' },
                { code: 'fr-FR', name: 'Français' }
            ];
        }
    }
    
    createUI() {
        // Create dropdown container
        const container = document.createElement('div');
        container.className = 'language-selector';
        container.innerHTML = `
            <button class="language-toggle-btn" id="language-toggle">
                <span class="language-icon">🌐</span>
                <span class="language-text">${this.getCurrentLanguageName()}</span>
                <span class="dropdown-arrow">▼</span>
            </button>
            <div class="language-dropdown" id="language-dropdown">
                ${this.languages.map(lang => `
                    <div class="language-option ${lang.code === this.currentLang ? 'active' : ''}" 
                         data-lang="${lang.code}">
                        <span class="language-name">${lang.name}</span>
                        ${lang.code === this.currentLang ? '<span class="check-mark">✓</span>' : ''}
                    </div>
                `).join('')}
            </div>
        `;
        
        // Find the existing language button and replace it
        const existingBtn = document.getElementById('language-toggle');
        if (existingBtn) {
            existingBtn.parentNode.replaceChild(container, existingBtn);
        } else {
            // Add to user info section
            const userInfo = document.querySelector('.user-info');
            if (userInfo) {
                userInfo.appendChild(container);
            }
        }
        
        // Add styles
        this.addStyles();
    }
    
    addStyles() {
        if (document.getElementById('language-selector-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'language-selector-styles';
        styles.textContent = `
            .language-selector {
                position: relative;
                display: inline-block;
            }
            
            .language-toggle-btn {
                background: #8f7a66;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: background 0.2s ease;
                margin-left: 8px;
                display: flex;
                align-items: center;
                gap: 4px;
                min-width: 80px;
            }
            
            .language-toggle-btn:hover {
                background: #776e65;
            }
            
            .language-toggle-btn.open {
                background: #776e65;
            }
            
            .language-icon {
                font-size: 0.8rem;
            }
            
            .language-text {
                flex: 1;
                text-align: left;
                font-size: 0.8rem;
            }
            
            .dropdown-arrow {
                font-size: 0.7rem;
                transition: transform 0.2s ease;
            }
            
            .language-toggle-btn.open .dropdown-arrow {
                transform: rotate(180deg);
            }
            
            .language-dropdown {
                position: absolute;
                top: 100%;
                right: 0;
                background: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                min-width: 150px;
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px);
                transition: all 0.2s ease;
            }
            
            .language-dropdown.open {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
            
            .language-option {
                padding: 10px 12px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #333;
                font-size: 0.9rem;
                transition: background 0.2s ease;
            }
            
            .language-option:hover {
                background: #f5f5f5;
            }
            
            .language-option.active {
                background: #e8f4f8;
                color: #2196F3;
            }
            
            .language-option:first-child {
                border-radius: 6px 6px 0 0;
            }
            
            .language-option:last-child {
                border-radius: 0 0 6px 6px;
            }
            
            .check-mark {
                color: #2196F3;
                font-weight: bold;
            }
            
            @media (max-width: 600px) {
                .language-toggle-btn {
                    padding: 6px 8px;
                    font-size: 0.8rem;
                    min-width: 60px;
                }
                
                .language-text {
                    display: none;
                }
                
                .language-dropdown {
                    min-width: 120px;
                }
            }
        `;
        document.head.appendChild(styles);
    }
    
    setupEventListeners() {
        const toggleBtn = document.getElementById('language-toggle');
        const dropdown = document.getElementById('language-dropdown');
        
        if (!toggleBtn || !dropdown) return;
        
        // Toggle dropdown
        toggleBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown();
        });
        
        // Language selection
        dropdown.addEventListener('click', (e) => {
            const option = e.target.closest('.language-option');
            if (option) {
                const lang = option.dataset.lang;
                this.selectLanguage(lang);
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            this.closeDropdown();
        });
        
        // Close dropdown on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDropdown();
            }
        });
    }
    
    toggleDropdown() {
        this.isOpen = !this.isOpen;
        this.updateDropdownState();
    }
    
    closeDropdown() {
        if (this.isOpen) {
            this.isOpen = false;
            this.updateDropdownState();
        }
    }
    
    updateDropdownState() {
        const toggleBtn = document.getElementById('language-toggle');
        const dropdown = document.getElementById('language-dropdown');
        
        if (toggleBtn && dropdown) {
            toggleBtn.classList.toggle('open', this.isOpen);
            dropdown.classList.toggle('open', this.isOpen);
        }
    }
    
    async selectLanguage(lang) {
        if (lang === this.currentLang) {
            this.closeDropdown();
            return;
        }
        
        try {
            // Switch language using i18n manager
            if (window.i18n) {
                const success = await window.i18n.switchLanguage(lang);
                if (success) {
                    this.currentLang = lang;
                    this.updateUI();
                    this.closeDropdown();
                    
                    // Reload page to update server-side translations
                    setTimeout(() => {
                        window.location.reload();
                    }, 100);
                }
            }
        } catch (error) {
            console.error('Failed to switch language:', error);
        }
    }
    
    getCurrentLanguageName() {
        const lang = this.languages.find(l => l.code === this.currentLang);
        return lang ? lang.name : this.currentLang;
    }
    
    updateUI() {
        const langText = document.querySelector('.language-text');
        if (langText) {
            langText.textContent = this.getCurrentLanguageName();
        }
        
        // Update active state in dropdown
        const options = document.querySelectorAll('.language-option');
        options.forEach(option => {
            const isActive = option.dataset.lang === this.currentLang;
            option.classList.toggle('active', isActive);
            
            const checkMark = option.querySelector('.check-mark');
            if (isActive && !checkMark) {
                option.innerHTML += '<span class="check-mark">✓</span>';
            } else if (!isActive && checkMark) {
                checkMark.remove();
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.languageSelector = new LanguageSelector();
});
