// Frontend Internationalization Manager
class I18nManager {
    constructor() {
        this.currentLang = 'en-US';
        this.messages = {};
        this.fallbackLang = 'en-US';
        this.supportedLanguages = ['zh-CN', 'en-US', 'ja-<PERSON>', 'ko-KR', 'fr-FR'];
        this.loadPromises = new Map();
        
        // Initialize with browser/server language
        this.init();
    }
    
    async init() {
        // Get language from various sources
        const lang = this.detectLanguage();
        await this.setLanguage(lang);
    }
    
    detectLanguage() {
        // 1. Check URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const urlLang = urlParams.get('lang');
        if (urlLang && this.supportedLanguages.includes(urlLang)) {
            return urlLang;
        }
        
        // 2. Check localStorage
        const storedLang = localStorage.getItem('lang');
        if (storedLang && this.supportedLanguages.includes(storedLang)) {
            return storedLang;
        }
        
        // 3. Check document lang attribute (set by server)
        const docLang = document.documentElement.lang;
        if (docLang && this.supportedLanguages.includes(docLang)) {
            return docLang;
        }
        
        // 4. Check browser language
        const browserLang = this.parseBrowserLanguage();
        if (browserLang && this.supportedLanguages.includes(browserLang)) {
            return browserLang;
        }
        
        // 5. Fallback to default
        return this.fallbackLang;
    }
    
    parseBrowserLanguage() {
        const lang = navigator.language || navigator.userLanguage;
        if (!lang) return null;
        
        // Language mapping
        const langMap = {
            'zh': 'zh-CN',
            'zh-cn': 'zh-CN',
            'zh-tw': 'zh-CN',
            'zh-hk': 'zh-CN',
            'en': 'en-US',
            'en-us': 'en-US',
            'en-gb': 'en-US',
            'ja': 'ja-JP',
            'ja-jp': 'ja-JP',
            'ko': 'ko-KR',
            'ko-kr': 'ko-KR',
            'fr': 'fr-FR',
            'fr-fr': 'fr-FR'
        };
        
        const normalizedLang = lang.toLowerCase();
        return langMap[normalizedLang] || langMap[normalizedLang.split('-')[0]] || null;
    }
    
    async loadLanguage(lang) {
        if (this.messages[lang]) {
            return this.messages[lang];
        }
        
        // Prevent duplicate loading
        if (this.loadPromises.has(lang)) {
            return this.loadPromises.get(lang);
        }
        
        const loadPromise = this.fetchLanguageData(lang);
        this.loadPromises.set(lang, loadPromise);
        
        try {
            const messages = await loadPromise;
            this.messages[lang] = messages;
            return messages;
        } catch (error) {
            console.error(`Failed to load language ${lang}:`, error);
            this.loadPromises.delete(lang);
            throw error;
        }
    }
    
    async fetchLanguageData(lang) {
        const response = await fetch(`/static/locales/${lang}.json`);
        if (!response.ok) {
            throw new Error(`Failed to fetch language data for ${lang}`);
        }
        return response.json();
    }
    
    async setLanguage(lang) {
        if (!this.supportedLanguages.includes(lang)) {
            console.warn(`Unsupported language: ${lang}, falling back to ${this.fallbackLang}`);
            lang = this.fallbackLang;
        }
        
        try {
            await this.loadLanguage(lang);
            this.currentLang = lang;
            
            // Save to localStorage
            localStorage.setItem('lang', lang);
            
            // Update document language
            document.documentElement.lang = lang;
            
            // Trigger language change event
            this.triggerLanguageChange(lang);
            
            return true;
        } catch (error) {
            console.error('Failed to set language:', error);
            return false;
        }
    }
    
    t(key, params = {}) {
        const message = this.getMessage(key, this.currentLang) || 
                       this.getMessage(key, this.fallbackLang) || 
                       key;
        
        return this.interpolate(message, params);
    }
    
    getMessage(key, lang) {
        const messages = this.messages[lang];
        if (!messages) return null;
        
        // Support nested keys like 'game.score'
        const keys = key.split('.');
        let value = messages;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return null;
            }
        }
        
        return typeof value === 'string' ? value : null;
    }
    
    interpolate(message, params) {
        if (!params || Object.keys(params).length === 0) {
            return message;
        }
        
        return message.replace(/\{(\w+)(?::([^}]+))?\}/g, (match, key, format) => {
            if (!(key in params)) return match;
            
            const value = params[key];
            
            // Apply formatting if specified
            if (format) {
                return this.formatValue(value, format);
            }
            
            return String(value);
        });
    }
    
    formatValue(value, format) {
        switch (format) {
            case 'n': // Number formatting
                return typeof value === 'number' ? value.toLocaleString(this.currentLang) : value;
            default:
                return String(value);
        }
    }
    
    triggerLanguageChange(lang) {
        const event = new CustomEvent('languagechange', {
            detail: { language: lang, i18n: this }
        });
        document.dispatchEvent(event);
    }
    
    getCurrentLanguage() {
        return this.currentLang;
    }
    
    getSupportedLanguages() {
        return [...this.supportedLanguages];
    }
    
    async switchLanguage(lang) {
        const success = await this.setLanguage(lang);
        if (success) {
            // Notify server about language change
            this.notifyServerLanguageChange(lang);
        }
        return success;
    }
    
    notifyServerLanguageChange(lang) {
        // Send language preference to server
        fetch('/api/language', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `lang=${encodeURIComponent(lang)}`
        }).catch(error => {
            console.warn('Failed to notify server about language change:', error);
        });
    }
}

// Global instance
window.i18n = new I18nManager();

// Global translation function
window.t = (key, params) => window.i18n.t(key, params);

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = I18nManager;
}
