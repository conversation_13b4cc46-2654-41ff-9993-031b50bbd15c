/* Main CSS for 2048 Game */

* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background: #faf8ef;
    color: #776e65;
    line-height: 1.6;
}

.container {
    min-height: 100vh;
}

/* Game Tiles */
.tile {
    background: rgba(238, 228, 218, 0.35);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    transition: all 0.15s ease-in-out;
    aspect-ratio: 1;
    position: relative;
}

.tile-empty {
    background: rgba(238, 228, 218, 0.35);
}

.tile-2 {
    background: #eee4da;
    color: #776e65;
    font-size: 1.8rem;
}

.tile-4 {
    background: #ede0c8;
    color: #776e65;
    font-size: 1.8rem;
}

.tile-8 {
    background: #f2b179;
    color: #f9f6f2;
    font-size: 1.8rem;
}

.tile-16 {
    background: #f59563;
    color: #f9f6f2;
    font-size: 1.7rem;
}

.tile-32 {
    background: #f67c5f;
    color: #f9f6f2;
    font-size: 1.7rem;
}

.tile-64 {
    background: #f65e3b;
    color: #f9f6f2;
    font-size: 1.6rem;
}

.tile-128 {
    background: #edcf72;
    color: #f9f6f2;
    font-size: 1.4rem;
    box-shadow: 0 0 30px 10px rgba(243, 215, 116, 0.2);
}

.tile-256 {
    background: #edcc61;
    color: #f9f6f2;
    font-size: 1.4rem;
    box-shadow: 0 0 30px 10px rgba(243, 215, 116, 0.3);
}

.tile-512 {
    background: #edc850;
    color: #f9f6f2;
    font-size: 1.4rem;
    box-shadow: 0 0 30px 10px rgba(243, 215, 116, 0.4);
}

.tile-1024 {
    background: #edc53f;
    color: #f9f6f2;
    font-size: 1.2rem;
    box-shadow: 0 0 30px 10px rgba(243, 215, 116, 0.5);
}

.tile-2048 {
    background: #edc22e;
    color: #f9f6f2;
    font-size: 1.2rem;
    box-shadow: 0 0 30px 10px rgba(243, 215, 116, 0.6);
}

.tile-4096 {
    background: #3c3a32;
    color: #f9f6f2;
    font-size: 1.1rem;
    box-shadow: 0 0 30px 10px rgba(60, 58, 50, 0.4);
}

.tile-8192 {
    background: #000000;
    color: #f9f6f2;
    font-size: 1.1rem;
    box-shadow: 0 0 30px 10px rgba(0, 0, 0, 0.6);
}

.tile-16384 {
    background: #ff6b6b;
    color: #f9f6f2;
    font-size: 1.0rem;
    box-shadow: 0 0 40px 15px rgba(255, 107, 107, 0.8);
    animation: victory-glow 2s ease-in-out infinite alternate;
}

@keyframes victory-glow {
    from {
        box-shadow: 0 0 30px 10px rgba(255, 215, 0, 0.6);
    }
    to {
        box-shadow: 0 0 50px 20px rgba(255, 215, 0, 0.8);
    }
}

/* Leaderboard Styles */
.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.leaderboard-entry {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: #f8f8f8;
    border-radius: 8px;
    transition: background 0.2s ease;
}

.leaderboard-entry:hover {
    background: #f0f0f0;
}

.leaderboard-entry.current-user {
    background: #e3f2fd;
    border: 2px solid #2196f3;
}

.leaderboard-entry.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
}

.leaderboard-entry.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
    color: #333;
}

.leaderboard-entry.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
    color: #333;
}

.leaderboard-entry .rank {
    font-size: 1.2rem;
    font-weight: 700;
    min-width: 40px;
    text-align: center;
}

.leaderboard-entry .user-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 15px;
}

.leaderboard-entry .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.leaderboard-entry .user-name {
    font-weight: 500;
}

.leaderboard-entry .you-badge {
    background: #2196f3;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
}

.leaderboard-entry .score {
    font-size: 1.1rem;
    font-weight: 700;
    color: #776e65;
}

.empty-leaderboard {
    text-align: center;
    padding: 40px 20px;
    color: #8f7a66;
}

.empty-leaderboard p {
    margin: 10px 0;
}

/* Loading Styles */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #8f7a66;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #8f7a66;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Styles */
button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.2s ease;
}

button:hover {
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

/* Responsive Design */
@media (max-width: 768px) {
    .tile {
        font-size: 1.5rem;
    }
    
    .tile-2, .tile-4, .tile-8 {
        font-size: 1.3rem;
    }
    
    .tile-16, .tile-32, .tile-64 {
        font-size: 1.2rem;
    }
    
    .tile-128, .tile-256, .tile-512 {
        font-size: 1rem;
    }
    
    .tile-1024, .tile-2048 {
        font-size: 0.9rem;
    }
    
    .tile-4096, .tile-8192 {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .tile {
        font-size: 1.2rem;
    }
    
    .tile-2, .tile-4, .tile-8 {
        font-size: 1rem;
    }
    
    .tile-16, .tile-32, .tile-64 {
        font-size: 0.9rem;
    }
    
    .tile-128, .tile-256, .tile-512 {
        font-size: 0.8rem;
    }
    
    .tile-1024, .tile-2048 {
        font-size: 0.7rem;
    }
    
    .tile-4096, .tile-8192 {
        font-size: 0.6rem;
    }
}
