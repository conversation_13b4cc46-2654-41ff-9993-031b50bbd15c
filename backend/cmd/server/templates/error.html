<!DOCTYPE html>
<html lang="{{.lang}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{call .t "error_title"}} - {{call .t "game_title"}}</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #faf8ef 0%, #f2efe6 100%);
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .error-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #776e65;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }
        
        .error-message {
            color: #8f7a66;
            margin-bottom: 30px;
            line-height: 1.5;
            background: #f8f8f8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f44336;
        }
        
        .error-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #8f7a66;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: background 0.2s ease;
            display: inline-block;
        }
        
        .btn:hover {
            background: #776e65;
        }
        
        .btn-secondary {
            background: #bbada0;
        }
        
        .btn-secondary:hover {
            background: #a39489;
        }
        
        .error-details {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
            color: #8f7a66;
        }
        
        @media (max-width: 480px) {
            .error-container {
                padding: 30px 20px;
            }
            
            .error-actions {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">❌</div>
        <h1>{{call .t "error_title"}}</h1>

        <div class="error-message">
            {{.error}}
        </div>

        <div class="error-actions">
            <a href="/" class="btn">{{call .t "error_go_home"}}</a>
            <button onclick="history.back()" class="btn btn-secondary">{{call .t "error_go_back"}}</button>
        </div>

        <div class="error-details">
            <p>{{call .t "error_help_title"}}</p>
            <ul style="text-align: left; display: inline-block;">
                <li>{{call .t "error_help_refresh"}}</li>
                <li>{{call .t "error_help_cache"}}</li>
                <li>{{call .t "error_help_connection"}}</li>
                <li>{{call .t "error_help_wait"}}</li>
            </ul>
        </div>
    </div>
</body>
</html>
