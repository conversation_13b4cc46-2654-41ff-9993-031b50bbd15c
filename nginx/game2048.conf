# Nginx configuration for 2048 Game
# Place this file in /etc/nginx/sites-available/ and symlink to /etc/nginx/sites-enabled/

# Upstream backend servers
upstream game2048_backend {
    # If running locally
    server 127.0.0.1:8080;
    
    # If running in Docker on same host
    # server 127.0.0.1:8080;
    
    # If running in Docker Compose
    # server game2048-backend:8080;
    
    # For load balancing (multiple instances)
    # server 127.0.0.1:8080;
    # server 127.0.0.1:8081;
    
    # Health check and failover
    keepalive 32;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=ws:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=1r/s;

# Main server block
server {
    listen 80;
    listen [::]:80;
    
    # Replace with your domain
    server_name your-domain.com www.your-domain.com;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Static file caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://game2048_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache static files for 1 year (versioned URLs handle cache busting)
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # CORS headers for static assets if needed
        add_header Access-Control-Allow-Origin "*";
    }
    
    # WebSocket connections
    location /ws {
        proxy_pass http://game2048_backend;
        
        # WebSocket specific headers
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket timeout settings
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
        
        # Rate limiting for WebSocket connections
        limit_req zone=ws burst=10 nodelay;
    }
    
    # API endpoints with rate limiting
    location /api/ {
        proxy_pass http://game2048_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Rate limiting for API calls
        limit_req zone=api burst=20 nodelay;
        
        # API response caching (optional)
        # proxy_cache api_cache;
        # proxy_cache_valid 200 5m;
        # proxy_cache_key "$scheme$request_method$host$request_uri";
    }
    
    # Authentication endpoints with stricter rate limiting
    location /auth/ {
        proxy_pass http://game2048_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Stricter rate limiting for auth endpoints
        limit_req zone=auth burst=5 nodelay;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://game2048_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # No rate limiting for health checks
        access_log off;
    }
    
    # All other requests (main app)
    location / {
        proxy_pass http://game2048_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Custom error pages (optional)
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# HTTPS redirect (uncomment when you have SSL certificate)
# server {
#     listen 80;
#     listen [::]:80;
#     server_name your-domain.com www.your-domain.com;
#     return 301 https://$server_name$request_uri;
# }

# HTTPS server block (uncomment and configure when you have SSL certificate)
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#     
#     # SSL certificate paths (adjust as needed)
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL configuration
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS header
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # Include all the location blocks from the HTTP server above
#     # ... (copy all location blocks from above)
# }
