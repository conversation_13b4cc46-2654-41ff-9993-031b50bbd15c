# 2048 游戏运行指南

## 🚀 快速开始

### 方法一：使用 Docker（推荐）

这是最简单的方式，一键启动所有服务：

```bash
# 1. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入您的 OAuth2 配置

# 2. 启动所有服务
docker-compose -f docker/docker-compose.yml up -d

# 3. 访问游戏
# 打开浏览器访问：http://localhost:6060
```

### 方法二：开发模式运行

适合开发和调试：

```bash
# 使用开发脚本（推荐）
./scripts/dev.sh

# 或者手动启动：
# 1. 启动数据库服务
docker-compose -f docker/docker-compose.yml up -d postgres redis

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 3. 启动后端服务
cd backend
go run cmd/server/main.go

# 4. 访问游戏
# 打开浏览器访问：http://localhost:6060
```

## 📋 前置要求

### Docker 方式
- Docker
- Docker Compose

### 开发方式
- Go 1.21+
- Docker（用于数据库）

## ⚙️ 配置说明

### 1. OAuth2 配置

编辑 `.env` 文件，配置 OAuth2 登录：

#### 自定义 OAuth2 服务（推荐）
```env
OAUTH2_PROVIDER=custom
OAUTH2_CLIENT_ID=your_oauth2_client_id
OAUTH2_CLIENT_SECRET=your_oauth2_client_secret
OAUTH2_REDIRECT_URL=http://localhost:6060/auth/callback

# 自定义 OAuth2 端点
OAUTH2_AUTH_URL=https://your-oauth-server.com/oauth/authorize
OAUTH2_TOKEN_URL=https://your-oauth-server.com/oauth/token
OAUTH2_USERINFO_URL=https://your-oauth-server.com/api/user
OAUTH2_SCOPES=openid,profile,email

# 用户信息字段映射（根据您的 OAuth2 响应自定义）
OAUTH2_USER_ID_FIELD=id
OAUTH2_USER_EMAIL_FIELD=email
OAUTH2_USER_NAME_FIELD=name
OAUTH2_USER_AVATAR_FIELD=avatar
```

#### Google OAuth2
```env
OAUTH2_PROVIDER=google
OAUTH2_CLIENT_ID=your_google_client_id
OAUTH2_CLIENT_SECRET=your_google_client_secret
OAUTH2_REDIRECT_URL=http://localhost:6060/auth/callback
```

#### GitHub OAuth2
```env
OAUTH2_PROVIDER=github
OAUTH2_CLIENT_ID=your_github_client_id
OAUTH2_CLIENT_SECRET=your_github_client_secret
OAUTH2_REDIRECT_URL=http://localhost:6060/auth/callback
```

### 2. 获取 OAuth2 凭据

#### 自定义 OAuth2 服务配置

**基本要求：**
您的 OAuth2 服务需要支持标准的 OAuth2 授权码流程，包括：

1. **授权端点** (`OAUTH2_AUTH_URL`)：用户登录和授权的页面
2. **令牌端点** (`OAUTH2_TOKEN_URL`)：交换授权码获取访问令牌
3. **用户信息端点** (`OAUTH2_USERINFO_URL`)：获取用户信息的 API

**字段映射配置：**
根据您的 OAuth2 服务返回的用户信息格式，配置字段映射：

```env
# 如果用户信息 API 返回：
# {
#   "user_id": "12345",
#   "email": "<EMAIL>",
#   "display_name": "张三",
#   "profile_picture": "https://example.com/avatar.jpg"
# }

OAUTH2_USER_ID_FIELD=user_id
OAUTH2_USER_EMAIL_FIELD=email
OAUTH2_USER_NAME_FIELD=display_name
OAUTH2_USER_AVATAR_FIELD=profile_picture
```

**嵌套字段支持：**
支持使用点号访问嵌套字段：

```env
# 如果用户信息 API 返回：
# {
#   "id": "12345",
#   "profile": {
#     "name": "张三",
#     "avatar": {
#       "url": "https://example.com/avatar.jpg"
#     }
#   }
# }

OAUTH2_USER_ID_FIELD=id
OAUTH2_USER_NAME_FIELD=profile.name
OAUTH2_USER_AVATAR_FIELD=profile.avatar.url
```

**OAuth2 服务端配置：**
在您的 OAuth2 服务中添加重定向 URI：
- 开发环境：`http://localhost:6060/auth/callback`
- 生产环境：`https://yourdomain.com/auth/callback`

#### Google OAuth2
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 创建 OAuth2 客户端 ID
5. 添加重定向 URI：`http://localhost:6060/auth/callback`

#### GitHub OAuth2
1. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
2. 创建新的 OAuth App
3. 设置 Authorization callback URL：`http://localhost:6060/auth/callback`

## 🎮 使用说明

### 游戏规则
- 使用方向键或滑动手势移动方块
- 相同数字的方块会合并
- 目标是达到 8192 方块获得胜利
- 无法移动时游戏结束

### 功能特性
- **实时游戏**：WebSocket 通信，服务器端游戏逻辑
- **用户认证**：OAuth2 登录系统
- **排行榜**：日榜、周榜、月榜、总榜
- **移动适配**：响应式设计，支持触摸操作
- **数据持久化**：游戏进度自动保存

## 🛠️ 开发指南

### 项目结构
```
2048/
├── backend/                    # Go 后端服务
│   ├── cmd/server/            # 主程序
│   ├── internal/             # 内部包
│   └── pkg/                  # 公共包
├── docker/                   # Docker 配置
├── scripts/                  # 构建和部署脚本
└── docs/                     # 文档
```

### 开发命令

```bash
# 构建项目
./scripts/build.sh

# 部署项目
./scripts/deploy.sh

# 查看日志
docker-compose -f docker/docker-compose.yml logs -f

# 停止服务
docker-compose -f docker/docker-compose.yml down

# 重启服务
docker-compose -f docker/docker-compose.yml restart
```

### 数据库管理

```bash
# 连接到 PostgreSQL
docker-compose -f docker/docker-compose.yml exec postgres psql -U postgres -d game2048

# 查看数据库表
\dt

# 查看用户数据
SELECT * FROM users;

# 查看游戏数据
SELECT * FROM games ORDER BY score DESC LIMIT 10;
```

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :6060
   # 修改 .env 中的 SERVER_PORT
   ```

2. **OAuth2 认证失败**
   - 检查 `.env` 文件中的客户端 ID 和密钥
   - 确认重定向 URL 配置正确
   - 检查 OAuth2 应用的域名设置

3. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose -f docker/docker-compose.yml ps
   # 重启数据库
   docker-compose -f docker/docker-compose.yml restart postgres
   ```

4. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认服务器正常运行
   - 查看浏览器控制台错误信息

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker/docker-compose.yml logs

# 查看特定服务日志
docker-compose -f docker/docker-compose.yml logs backend
docker-compose -f docker/docker-compose.yml logs postgres

# 实时查看日志
docker-compose -f docker/docker-compose.yml logs -f
```

## 🌐 生产部署

### 环境变量配置
```env
# 生产环境配置
GIN_MODE=release
DEBUG=false
SERVER_HOST=0.0.0.0
DB_SSL_MODE=require

# 安全配置
JWT_SECRET=your-super-secure-jwt-secret-key
CORS_ORIGINS=https://yourdomain.com

# OAuth2 生产配置
OAUTH2_REDIRECT_URL=https://yourdomain.com/auth/callback
```

### HTTPS 配置
建议使用 Nginx 或 Traefik 作为反向代理，配置 SSL 证书。

### 性能优化
- 启用 Redis 缓存
- 配置数据库连接池
- 使用 CDN 加速静态资源

## 📞 技术支持

如果遇到问题，请检查：
1. 环境变量配置是否正确
2. 所有服务是否正常运行
3. 网络连接是否正常
4. 浏览器控制台是否有错误信息

祝您游戏愉快！🎮
