# Environment files
.env
.env.local
.env.production

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
vendor/
bin/
dist/

# Frontend
node_modules/
frontend/dist/
frontend/node_modules/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Bun
.bun/
bun.lockb

# Database
*.db
*.sqlite
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Build artifacts
backend/bin/

# Test coverage
coverage.out
*.cover

# Air (Go live reload)
.air.toml
tmp/
